# -*- coding: utf-8 -*-
"""
Annet Assistant 运维助手系统 - MySQL 数据库连接模块

该模块提供 MySQL 数据库的连接、查询、更新等功能。
支持连接池管理，确保数据库连接的高效使用。

作者: Annet Team
创建时间: 2025-08-01
版本: v1.0
"""

import pymysql
import logging
from typing import List, Dict, Any, Optional, Tuple
import time
from contextlib import contextmanager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MyConnectMySQL:
    """MySQL 数据库连接类
    
    提供数据库连接、查询、更新等功能，支持连接池管理。
    """
    
    def __init__(self):
        """初始化数据库连接配置"""
        self.config = {
            'host': 'annet-mysql1.mysql.rds.aliyuncs.com',
            'port': 3306,
            'user': 'master_annet',
            'password': '!@#Annet123',
            'database': 'annet_master20171116',
            'charset': 'utf8',
            'autocommit': True,
            'connect_timeout': 10,
            'read_timeout': 10,
            'write_timeout': 10
        }
        self.connection = None
        
    def connect(self) -> bool:
        """建立数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = pymysql.connect(**self.config)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("数据库连接已关闭")
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器
        
        Yields:
            pymysql.cursors.Cursor: 数据库游标
        """
        if not self.connection:
            if not self.connect():
                raise Exception("无法建立数据库连接")
        
        cursor = self.connection.cursor(pymysql.cursors.DictCursor)
        try:
            yield cursor
        finally:
            cursor.close()
    
    def quary_data(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行查询语句
        
        Args:
            sql (str): SQL 查询语句
            params (Optional[Tuple]): 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            with self.get_cursor() as cursor:
                cursor.execute(sql, params)
                result = cursor.fetchall()
                logger.info(f"查询成功，返回 {len(result)} 条记录")
                return result
        except Exception as e:
            logger.error(f"查询失败: {str(e)}")
            return []
    
    def execute_sql(self, sql: str, params: Optional[Tuple] = None) -> bool:
        """执行更新/插入/删除语句
        
        Args:
            sql (str): SQL 语句
            params (Optional[Tuple]): 参数
            
        Returns:
            bool: 执行是否成功
        """
        try:
            with self.get_cursor() as cursor:
                affected_rows = cursor.execute(sql, params)
                self.connection.commit()
                logger.info(f"SQL 执行成功，影响 {affected_rows} 行")
                return True
        except Exception as e:
            logger.error(f"SQL 执行失败: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def get_user_info_by_phone(self, phone: str) -> List[Dict[str, Any]]:
        """根据手机号查询用户基础信息
        
        Args:
            phone (str): 手机号
            
        Returns:
            List[Dict[str, Any]]: 用户信息列表
        """
        sql = """
        SELECT 
            ub.name,
            ub.role,
            ub.userid,
            ub.phone,
            u.modify_password_time,
            u.fail_num,
            u.status
        FROM user_baseinfo ub
        LEFT JOIN user u ON ub.userid = u.userid
        WHERE ub.phone = %s
        """
        return self.quary_data(sql, (phone,))
    
    def get_user_bind_info(self, search_key: str) -> List[Dict[str, Any]]:
        """查询用户关联信息（数据账户、组织信息等）
        
        Args:
            search_key (str): 搜索关键字（手机号或数据账户）
            
        Returns:
            List[Dict[str, Any]]: 关联信息列表
        """
        sql = """
        SELECT 
            ud.dataaccount,
            ud.name,
            ud.orgname,
            ud.deptname,
            ud.office,
            ud.userid,
            ud.phone,
            ub.role
        FROM user_dataaccount ud
        LEFT JOIN user_baseinfo ub ON ud.userid = ub.userid
        WHERE ud.phone = %s OR ud.dataaccount = %s
        ORDER BY ud.created_at DESC
        """
        return self.quary_data(sql, (search_key, search_key))
    
    def check_user_login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """验证用户登录
        
        Args:
            username (str): 用户名
            password (str): 密码
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息，登录失败返回 None
        """
        # 注意：这里应该使用哈希密码验证，示例中简化处理
        sql = """
        SELECT 
            ub.userid,
            ub.name,
            ub.role,
            ub.phone
        FROM user_baseinfo ub
        JOIN user u ON ub.userid = u.userid
        WHERE ub.userid = %s AND u.password = %s AND u.status = 1
        """
        result = self.quary_data(sql, (username, password))
        return result[0] if result else None
    
    def update_login_fail_count(self, userid: str, fail_count: int) -> bool:
        """更新用户登录失败次数
        
        Args:
            userid (str): 用户ID
            fail_count (int): 失败次数
            
        Returns:
            bool: 更新是否成功
        """
        sql = "UPDATE user SET fail_num = %s WHERE userid = %s"
        return self.execute_sql(sql, (fail_count, userid))
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        self.disconnect()


# 全局数据库连接实例
db_instance = MyConnectMySQL()


def get_db_connection() -> MyConnectMySQL:
    """获取数据库连接实例
    
    Returns:
        MyConnectMySQL: 数据库连接实例
    """
    return db_instance
