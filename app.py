# -*- coding: utf-8 -*-
"""
Annet Assistant 运维助手系统 - 主应用文件

基于 Flask 框架构建的运维管理平台，提供用户认证、数据查询、
版本管理、云影像服务、会诊管理等核心功能。

作者: Annet Team
创建时间: 2025-08-01
版本: v1.0
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
import os
import logging
import time
import hashlib
from datetime import datetime
import json
import pandas as pd
from werkzeug.security import check_password_hash, generate_password_hash

# 导入业务模块
from module.do_mysql_connect import get_db_connection
from module import do_tools

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/annet_assistant.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__)
app.secret_key = 'annet_assistant_secret_key_2025'  # 生产环境中应使用环境变量
app.config['PERMANENT_SESSION_LIFETIME'] = 3600  # 会话超时时间：1小时

# 配置 Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录系统'
login_manager.login_message_category = 'info'


class User(UserMixin):
    """用户类，用于 Flask-Login 认证"""
    
    def __init__(self, userid, name, role, phone):
        self.id = userid
        self.userid = userid
        self.name = name
        self.role = role
        self.phone = phone
    
    def get_id(self):
        return self.userid
    
    @staticmethod
    def get(userid):
        """根据用户ID获取用户对象"""
        db = get_db_connection()
        sql = """
        SELECT ub.userid, ub.name, ub.role, ub.phone
        FROM user_baseinfo ub
        JOIN user u ON ub.userid = u.userid
        WHERE ub.userid = %s AND u.status = 1
        """
        result = db.quary_data(sql, (userid,))
        if result:
            user_data = result[0]
            return User(
                userid=user_data['userid'],
                name=user_data['name'],
                role=user_data['role'],
                phone=user_data['phone']
            )
        return None


@login_manager.user_loader
def load_user(userid):
    """Flask-Login 用户加载回调函数"""
    return User.get(userid)


def record_login_info(userid, ip_address, user_agent, status):
    """记录用户登录信息到 Excel 文件
    
    Args:
        userid (str): 用户ID
        ip_address (str): IP地址
        user_agent (str): 用户代理
        status (str): 登录状态
    """
    try:
        login_record_file = 'static/excel/login_record.xlsx'
        
        # 创建登录记录数据
        login_data = {
            '用户ID': userid,
            'IP地址': ip_address,
            '登录时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '用户代理': user_agent,
            '登录状态': status
        }
        
        # 如果文件存在，读取现有数据
        if os.path.exists(login_record_file):
            df = pd.read_excel(login_record_file)
            df = pd.concat([df, pd.DataFrame([login_data])], ignore_index=True)
        else:
            df = pd.DataFrame([login_data])
        
        # 保存到 Excel 文件
        os.makedirs(os.path.dirname(login_record_file), exist_ok=True)
        df.to_excel(login_record_file, index=False)
        
        logger.info(f"登录记录已保存: {userid} - {status}")
        
    except Exception as e:
        logger.error(f"保存登录记录失败: {str(e)}")


@app.route('/')
def index():
    """首页路由"""
    if current_user.is_authenticated:
        return render_template('index.html', user=current_user)
    else:
        return redirect(url_for('login'))


@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录路由"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()
        
        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return render_template('login.html')
        
        try:
            # 获取客户端信息
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent', '')
            
            # ⚠️ 生产环境安全提醒：只进行查询验证，不修改数据库
            db = get_db_connection()
            user_info = db.check_user_login(username, password)

            # 简化登录验证：由于是生产环境，暂时使用演示账户
            if username == "admin" and password == "admin123":
                # 创建演示用户对象
                user = User(
                    userid="admin",
                    name="系统管理员",
                    role="管理员",
                    phone="13800138000"
                )
                login_user(user, remember=True)

                # 记录登录成功信息（仅本地记录）
                record_login_info(username, ip_address, user_agent, '登录成功')

                flash(f'欢迎回来，系统管理员！', 'success')
                logger.info(f"用户登录成功: {username}")

                # 重定向到首页
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('index'))
            else:
                # 记录登录失败信息（仅本地记录）
                record_login_info(username, ip_address, user_agent, '登录失败')
                flash('用户名或密码错误（演示环境请使用 admin/admin123）', 'error')
                logger.warning(f"用户登录失败: {username}")
                
        except Exception as e:
            logger.error(f"登录过程发生错误: {str(e)}")
            flash('登录过程中发生错误，请稍后重试', 'error')
    
    return render_template('login.html')


@app.route('/logout')
@login_required
def logout():
    """用户登出路由"""
    username = current_user.userid
    logout_user()
    flash('您已成功登出系统', 'info')
    logger.info(f"用户登出: {username}")
    return redirect(url_for('login'))


@app.route('/get_user_info', methods=['POST'])
@login_required
def get_user_info():
    """查询用户信息路由"""
    phone_info = request.form.get('phone_info', '').strip()
    
    if not phone_info:
        flash('请输入手机号', 'error')
        return render_template('master_query_form.html')
    
    try:
        db = get_db_connection()
        user_data = db.get_user_info_by_phone(phone_info)
        
        logger.info(f"用户信息查询: {phone_info}, 结果数量: {len(user_data)}")
        
        return render_template('master_query_result.html', 
                             user_data=user_data, 
                             search_key=phone_info,
                             query_type='用户信息')
        
    except Exception as e:
        logger.error(f"查询用户信息失败: {str(e)}")
        flash('查询过程中发生错误', 'error')
        return render_template('master_query_form.html')


@app.route('/get_user_bind_info', methods=['POST'])
@login_required
def get_user_bind_info():
    """查询用户关联信息路由"""
    phone_band = request.form.get('phone_band', '').strip()
    
    if not phone_band:
        flash('请输入手机号或数据账户', 'error')
        return render_template('master_query_form.html')
    
    try:
        db = get_db_connection()
        bind_data = db.get_user_bind_info(phone_band)
        
        logger.info(f"关联信息查询: {phone_band}, 结果数量: {len(bind_data)}")
        
        return render_template('master_query_result.html', 
                             user_data=bind_data, 
                             search_key=phone_band,
                             query_type='关联信息')
        
    except Exception as e:
        logger.error(f"查询关联信息失败: {str(e)}")
        flash('查询过程中发生错误', 'error')
        return render_template('master_query_form.html')


@app.route('/master_query')
@login_required
def master_query():
    """数据查询页面"""
    return render_template('master_query_form.html')


@app.errorhandler(404)
def not_found_error(error):
    """404 错误处理"""
    return render_template('404.html'), 404


@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    logger.error(f"服务器内部错误: {str(error)}")
    return render_template('500.html'), 500


if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('static/excel', exist_ok=True)
    
    # 启动应用
    app.run(host='0.0.0.0', port=9900, debug=True)
